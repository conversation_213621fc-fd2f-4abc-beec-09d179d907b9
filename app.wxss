/**app.wxss**/
/* 全局样式 */

/* iPhone 16 尺寸适配 */
page {
  --primary-color: #FB490E;
  --primary-gradient: linear-gradient(180deg, #F1F1F1 5.29%, #FFE8D7 59.62%, #FFCCA6 100%);
  --background-color: #FFFFFF;
  --card-background: #ffffff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-placeholder: #999999;
  --border-radius: 16rpx;
  --shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --status-bar-height: 44px;

  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: var(--primary-gradient);
  color: var(--text-primary);
  line-height: 1.4;
}

/* 通用容器 */
.container {
  min-height: 100vh;
  background-color: var(--background-color);
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 32rpx;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background: var(--primary-gradient);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
}

.btn-primary:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 53, 0.3);
}

/* 通用卡片样式 */
.card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
}

/* 通用文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-highlight {
  color: var(--primary-color);
}

/* 通用间距 */
.mt-xs { margin-top: 8rpx; }
.mt-sm { margin-top: 16rpx; }
.mt-md { margin-top: 24rpx; }
.mt-lg { margin-top: 32rpx; }

.mb-xs { margin-bottom: 8rpx; }
.mb-sm { margin-bottom: 16rpx; }
.mb-md { margin-bottom: 24rpx; }
.mb-lg { margin-bottom: 32rpx; }

.p-xs { padding: 8rpx; }
.p-sm { padding: 16rpx; }
.p-md { padding: 24rpx; }
.p-lg { padding: 32rpx; }

/* 通用flex布局 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 通用圆角 */
.rounded-sm { border-radius: 8rpx; }
.rounded-md { border-radius: 16rpx; }
.rounded-lg { border-radius: 24rpx; }
.rounded-full { border-radius: 50%; }

/* 通用阴影 */
.shadow-sm { box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.04); }
.shadow-md { box-shadow: var(--shadow); }
.shadow-lg { box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1); }
