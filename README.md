# 微信小程序 - 商品推荐页面

这是一个基于您提供的设计图创建的微信小程序前端页面，专为iPhone 16尺寸优化。

## 项目特点

### 🎨 设计还原
- 严格按照提供的UI设计图进行开发
- 使用温暖的橙色渐变色调 (#ff9a56 到 #ff6b35)
- 圆角设计和轻微阴影效果
- 现代化的卡片式布局

### 📱 iPhone 16 适配
- 专为iPhone 16尺寸优化
- 使用flexbox布局，无绝对定位
- 响应式设计，适配不同屏幕尺寸
- 符合微信小程序设计规范

### 🛠 技术实现
- **布局方式**: 100% flexbox布局，避免使用绝对定位
- **样式系统**: 使用WXSS，支持渐变、阴影、圆角等现代CSS特性
- **交互设计**: 包含点击反馈、过渡动画等微交互
- **组件化**: 模块化的代码结构，易于维护和扩展

## 页面结构

### 1. 搜索栏区域
- 橙色渐变背景
- 圆角搜索框
- 搜索图标和按钮

### 2. 功能图标区域
- 5个功能分类图标
- 圆形图标背景
- 展开按钮

### 3. 分类标签
- 可切换的分类标签
- 高亮显示当前选中项
- 排序功能指示

### 4. 商品列表
- 卡片式商品展示
- 商品图片、标题、价格、佣金信息
- 专属红包标签

## 文件结构

```
├── app.js              # 小程序入口文件
├── app.json            # 小程序配置文件
├── app.wxss            # 全局样式文件
├── sitemap.json        # 站点地图配置
├── pages/
│   └── index/
│       ├── index.js    # 页面逻辑
│       ├── index.json  # 页面配置
│       ├── index.wxml  # 页面结构
│       └── index.wxss  # 页面样式
├── images/             # 图片资源目录
└── README.md           # 项目说明
```

## 使用说明

1. **开发工具**: 使用微信开发者工具打开项目
2. **图片资源**: 请将实际的图片文件放置在 `images/` 目录中
3. **数据接口**: 在 `pages/index/index.js` 中的 `products` 数组可替换为实际的API数据
4. **样式调整**: 所有样式都在 `pages/index/index.wxss` 中，可根据需要进行微调

## 主要功能

- ✅ 搜索功能
- ✅ 分类导航
- ✅ 商品列表展示
- ✅ 商品点击跳转
- ✅ 分类筛选
- ✅ 排序功能
- ✅ 响应式布局

## 注意事项

1. 请确保在 `images/` 目录中放置所需的图片资源
2. 商品数据目前为静态数据，实际使用时需要连接后端API
3. 所有交互功能已预留事件处理函数，可根据业务需求进行扩展
4. 样式已针对iPhone 16进行优化，其他设备也能良好适配

## 下一步开发建议

1. 连接后端API获取真实商品数据
2. 实现搜索功能的后端对接
3. 添加商品详情页面
4. 实现购物车功能
5. 添加用户登录和个人中心
