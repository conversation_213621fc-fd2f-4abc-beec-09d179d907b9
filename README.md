# 微信小程序 - 商品推荐页面

这是一个严格按照Figma设计稿创建的微信小程序前端页面，专为iPhone 16尺寸优化，完全还原设计稿的视觉效果。

## 项目特点

### 🎨 严格设计还原
- **100%按照Figma设计稿开发** - 从Figma获取真实图片资源和精确尺寸
- **精确的颜色和渐变** - 使用设计稿中的确切颜色值和渐变效果
- **真实图片资源** - 直接从Figma导出的SVG图标和PNG图片
- **像素级精确布局** - 严格按照设计稿的尺寸和间距实现

### 📱 iPhone 16 完美适配
- **原生尺寸**: 393×852px (786×1704rpx) 完美匹配iPhone 16
- **Flexbox布局**: 100%使用flexbox，无绝对定位
- **响应式设计**: 适配不同屏幕尺寸的微信小程序环境
- **符合规范**: 遵循微信小程序设计和开发规范

### 🛠 技术实现
- **布局方式**: 100% flexbox布局，避免使用绝对定位
- **样式系统**: 使用WXSS，精确还原Figma设计稿样式
- **图片资源**: 从Figma直接导出的高质量SVG和PNG资源
- **交互设计**: 包含点击反馈、状态切换等微交互
- **组件化**: 模块化的代码结构，易于维护和扩展

## 页面结构

### 1. 顶部渐变背景区域
- **渐变背景**: 从#F1F1F1到#FFE8D7再到#FFCCA6的精美渐变
- **搜索栏**: 白色背景，橙色边框，内含搜索图标和按钮
- **功能图标**: 5个圆形头像图标，代表不同商品分类

### 2. 主要内容区域
- **左侧分类栏**: 灰色背景的垂直分类列表
- **右侧商品区**: 包含排序标签和商品列表

### 3. 左侧分类导航
- 垂直排列的分类选项
- 当前选中项高亮显示
- 包含全部美妆、精华水乳、口红唇釉等分类

### 4. 右侧内容区域
- **排序标签**: 综合、销量、佣金排序选项
- **商品列表**: 横向布局的商品卡片
- **商品信息**: 图片、标题、专属补贴标签、价格、佣金

## 文件结构

```
├── app.js              # 小程序入口文件
├── app.json            # 小程序配置文件
├── app.wxss            # 全局样式文件
├── sitemap.json        # 站点地图配置
├── pages/
│   └── index/
│       ├── index.js    # 页面逻辑
│       ├── index.json  # 页面配置
│       ├── index.wxml  # 页面结构
│       └── index.wxss  # 页面样式
├── images/             # 图片资源目录
└── README.md           # 项目说明
```

## 使用说明

1. **开发工具**: 使用微信开发者工具打开项目
2. **图片资源**: 请将实际的图片文件放置在 `images/` 目录中
3. **数据接口**: 在 `pages/index/index.js` 中的 `products` 数组可替换为实际的API数据
4. **样式调整**: 所有样式都在 `pages/index/index.wxss` 中，可根据需要进行微调

## 📋 实现的功能

✅ **搜索功能** - 从Figma导出的搜索图标，精确的搜索框样式
✅ **功能导航** - 5个真实头像图标（美妆护肤、服饰内衣、食品生鲜、母婴玩具、数码家电）
✅ **左侧分类** - 垂直分类导航，支持分类切换
✅ **排序功能** - 综合、销量、佣金排序，带方向指示箭头
✅ **商品列表** - 横向布局商品卡片，包含图片、标题、标签、价格、佣金
✅ **交互反馈** - 点击效果、状态切换
✅ **响应式布局** - 完美适配iPhone 16尺寸

## 🎨 设计特点

- **精确颜色**: 严格使用Figma设计稿中的颜色值
- **真实渐变**: #F1F1F1 → #FFE8D7 → #FFCCA6 的三色渐变
- **原生图标**: 从Figma直接导出的SVG和PNG资源
- **像素完美**: 所有尺寸和间距都按照设计稿精确实现
- **Flexbox布局**: 完全响应式，无绝对定位
- **微交互**: 符合设计稿的交互效果

## 📁 图片资源

项目已包含从Figma直接导出的真实图片资源：
- `search-icon.svg` - 搜索图标
- `align-right-icon.svg` - 展开按钮图标
- `avatar-24.png` - 头像图标1（美妆护肤）
- `avatar-8.png` - 头像图标2（服饰内衣）
- `avatar-30.png` - 头像图标3（食品生鲜）
- `product-1.png` - 商品图片

## 注意事项

1. ✅ 图片资源已从Figma导出并放置在 `images/` 目录中
2. 商品数据目前为静态数据，实际使用时需要连接后端API
3. 所有交互功能已预留事件处理函数，可根据业务需求进行扩展
4. 样式已严格按照Figma设计稿实现，完美适配iPhone 16

## 下一步开发建议

1. 连接后端API获取真实商品数据
2. 实现搜索功能的后端对接
3. 添加商品详情页面
4. 实现购物车功能
5. 添加用户登录和个人中心
