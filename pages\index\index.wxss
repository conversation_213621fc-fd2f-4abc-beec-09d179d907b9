/* iPhone 16 尺寸适配 - 严格按照Figma设计稿 */
page {
  background-color: #FFFFFF;
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  width: 786rpx;
  height: 1704rpx;
}

.container {
  display: flex;
  flex-direction: column;
  width: 786rpx;
  height: 1704rpx;
  background-color: #FFFFFF;
  position: relative;
}

/* 顶部渐变背景 */
.header-background {
  background: linear-gradient(180deg, #F1F1F1 5.29%, #FFE8D7 59.62%, #FFCCA6 100%);
  width: 786rpx;
  height: 516rpx;
  position: relative;
}

/* 搜索栏样式 */
.search-container {
  padding: 40rpx 26rpx 0;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border: 2rpx solid #FB490E;
  border-radius: 40rpx;
  width: 528rpx;
  height: 66rpx;
  padding: 0 22rpx;
  position: relative;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-family: Inter;
  font-weight: 500;
  font-size: 28rpx;
  line-height: 34rpx;
  color: #94908E;
  border: none;
  outline: none;
  background: transparent;
}

.search-placeholder {
  color: #94908E;
}

.search-btn {
  background: transparent;
  color: #FB490E;
  font-family: Inter;
  font-weight: 600;
  font-size: 30rpx;
  line-height: 36rpx;
  border: none;
  outline: none;
  cursor: pointer;
}

/* 功能图标区域 */
.function-icons {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 40rpx 30rpx 0;
  position: relative;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 94rpx;
}

.icon-wrapper {
  width: 94rpx;
  height: 94rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.icon {
  width: 94rpx;
  height: 94rpx;
  border-radius: 50%;
}

.icon-text {
  font-family: Inter;
  font-weight: 500;
  font-size: 22rpx;
  line-height: 26rpx;
  color: #FFFFFF;
  text-align: center;
  background-color: #FB490E;
  padding: 4rpx 16rpx;
  border-radius: 14rpx;
  white-space: nowrap;
}

.icon-item:first-child .icon-text {
  background-color: #FB490E;
  color: #FFFFFF;
}

.icon-item:not(:first-child) .icon-text {
  background-color: transparent;
  color: #090909;
}

.expand-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  right: 30rpx;
  top: 40rpx;
}

.expand-text {
  font-family: Inter;
  font-weight: 600;
  font-size: 32rpx;
  line-height: 38rpx;
  color: #191817;
  margin-bottom: 8rpx;
}

.expand-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex: 1;
  background-color: #FFFFFF;
  position: relative;
  top: -96rpx;
}

/* 左侧分类栏 */
.left-sidebar {
  width: 190rpx;
  background-color: #F4F4F4;
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
}

.category-item {
  font-family: Inter;
  font-weight: 500;
  font-size: 28rpx;
  line-height: 34rpx;
  color: #090909;
  padding: 30rpx 28rpx;
  text-align: left;
  cursor: pointer;
  border-bottom: 2rpx solid #ECECEC;
}

.category-item.active {
  color: #FB490E;
  font-weight: 600;
  background-color: #FFFFFF;
}

/* 右侧内容区域 */
.right-content {
  flex: 1;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
}

/* 排序标签 */
.sort-tabs {
  display: flex;
  align-items: center;
  padding: 40rpx;
  border-bottom: 2rpx solid #ECECEC;
}

.sort-item {
  display: flex;
  align-items: center;
  font-family: Inter;
  font-weight: 500;
  font-size: 30rpx;
  line-height: 36rpx;
  color: #424242;
  margin-right: 80rpx;
  cursor: pointer;
}

.sort-item.active {
  color: #FB490E;
}

.sort-arrow {
  width: 0;
  height: 0;
  margin-left: 8rpx;
}

.sort-arrow.up {
  border-left: 7rpx solid transparent;
  border-right: 7rpx solid transparent;
  border-bottom: 14rpx solid #FB490E;
}

.sort-arrow.down {
  border-left: 6rpx solid transparent;
  border-right: 6rpx solid transparent;
  border-top: 14rpx solid #D9D9D9;
}

/* 商品列表 */
.product-list {
  flex: 1;
  padding: 0 20rpx;
  overflow-y: auto;
}

.product-item {
  display: flex;
  align-items: flex-start;
  padding: 26rpx 0;
  border-bottom: 2rpx solid #ECECEC;
  cursor: pointer;
}

.product-item:active {
  background-color: #f9f9f9;
}

.product-image-container {
  width: 154rpx;
  height: 154rpx;
  border-radius: 10rpx;
  overflow: hidden;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.product-image {
  width: 154rpx;
  height: 154rpx;
  border-radius: 10rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 154rpx;
}

.product-title {
  font-family: Inter;
  font-weight: 500;
  font-size: 24rpx;
  line-height: 30rpx;
  color: #000000;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  width: 382rpx;
  height: 64rpx;
}

.product-tags {
  margin-bottom: 16rpx;
}

.tag {
  background-color: rgba(217, 217, 217, 0.07);
  border: 2rpx solid #FF3F00;
  border-radius: 6rpx;
  color: #FF3F00;
  font-family: Inter;
  font-weight: 500;
  font-size: 20rpx;
  line-height: 24rpx;
  padding: 4rpx 8rpx;
  display: inline-block;
}

.product-price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.price {
  font-family: Inter;
  font-weight: 600;
  font-size: 24rpx;
  line-height: 30rpx;
  color: #0B0B0B;
}

.commission-wrapper {
  display: flex;
  align-items: center;
  background-color: #FFE4DB;
  border-radius: 4rpx;
  padding: 6rpx 12rpx;
}

.commission-label {
  font-family: Inter;
  font-weight: 600;
  font-size: 20rpx;
  line-height: 24rpx;
  color: #FF3F00;
  margin-right: 8rpx;
}

.commission-price {
  font-family: Inter;
  font-weight: 500;
  font-size: 24rpx;
  line-height: 30rpx;
  color: #FF3F00;
}
