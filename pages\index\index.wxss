/* iPhone 16 尺寸适配 - 严格按照Figma设计稿 */
page {
  background: linear-gradient(180deg, #F1F1F1 5.29%, #FFE8D7 59.62%, #FFCCA6 100%);
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  width: 100%;
  min-height: 100vh;
}

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  position: relative;
  padding-bottom: 100rpx;
}

/* 顶部渐变背景 */
.header-background {
  background: linear-gradient(180deg, #F1F1F1 5.29%, #FFE8D7 59.62%, #FFCCA6 100%);
  width: 100%;
  padding-top: calc(var(--status-bar-height) + 44px);
  padding-bottom: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
}

/* 搜索栏样式 */
.search-container {
  padding: 0 40rpx 0;
  margin-top: 0;
  position: fixed;
  top: calc(var(--status-bar-height) + 2px);
  left: 0;
  right: 0;
  z-index: 100;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border: 2rpx solid #FB490E;
  border-radius: 40rpx;
  height: 66rpx;
  padding: 0 22rpx;
  margin-right: 160rpx;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-family: Inter;
  font-weight: 500;
  font-size: 28rpx;
  line-height: 34rpx;
  color: #94908E;
  border: none;
  outline: none;
  background: transparent;
}

.search-placeholder {
  color: #94908E;
}

.search-btn {
  background: transparent;
  color: #FB490E;
  font-family: Inter;
  font-weight: 600;
  font-size: 30rpx;
  line-height: 36rpx;
  border: none;
  outline: none;
  cursor: pointer;
}

/* 功能图标区域 */
.function-icons {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 30rpx 30rpx 40rpx;
  position: relative;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 94rpx;
  cursor: pointer;
}

.icon-wrapper {
  width: 94rpx;
  height: 94rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.icon {
  width: 94rpx;
  height: 94rpx;
  border-radius: 50%;
}

.icon-text {
  font-family: Inter;
  font-weight: 500;
  font-size: 22rpx;
  line-height: 26rpx;
  color: #090909;
  text-align: center;
  background-color: transparent;
  padding: 4rpx 16rpx;
  border-radius: 14rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
}

/* 选中状态的图标文字 */
.icon-item.active .icon-text {
  background-color: #FB490E;
  color: #FFFFFF;
}

/* 展开的图标区域 */
.expanded-icons {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  padding: 0 30rpx 40rpx;
  flex-wrap: wrap;
}

.expanded-icons .icon-item {
  margin-right: 67rpx;
}

.expanded-icons .icon-item:nth-child(5n) {
  margin-right: 0;
}

.expand-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  width: 94rpx;
  justify-content: flex-end;
}

.expand-text {
  font-family: Inter;
  font-weight: 600;
  font-size: 26rpx;
  line-height: 30rpx;
  color: #191817;
  margin-bottom: 16rpx;
  text-align: center;
  white-space: pre-line;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-icon {
  width: 42rpx;
  height: 42rpx;
  transition: transform 0.3s ease;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex: 1;
  background-color: #FFFFFF;
  position: relative;
  margin-top: 360rpx;
}

/* 左侧分类栏 */
.left-sidebar {
  width: 190rpx;
  background-color: #F4F4F4;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 360rpx;
  left: 0;
  bottom: 0;
  z-index: 30;
  overflow-y: auto;
}

.category-item {
  font-family: Inter;
  font-weight: 500;
  font-size: 28rpx;
  line-height: 34rpx;
  color: #090909;
  padding: 30rpx 28rpx;
  text-align: left;
  cursor: pointer;
  border-bottom: 2rpx solid #ECECEC;
  transition: all 0.3s ease;
}

.category-item.active {
  color: #FB490E;
  font-weight: 600;
  background-color: #FFFFFF;
}

/* 右侧内容区域 */
.right-content {
  flex: 1;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
}

/* 排序标签 */
.sort-tabs {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 2rpx solid #ECECEC;
  background-color: #FFFFFF;
  position: fixed;
  top: 360rpx;
  left: 190rpx;
  right: 0;
  z-index: 40;
  margin-top: 0;
}

.sort-item {
  display: flex;
  align-items: center;
  font-family: Inter;
  font-weight: 500;
  font-size: 30rpx;
  line-height: 36rpx;
  color: #424242;
  margin-right: 80rpx;
  cursor: pointer;
  transition: color 0.3s ease;
}

.sort-item.active {
  color: #FB490E;
  font-weight: 600;
}

.sort-arrow {
  width: 0;
  height: 0;
  margin-left: 8rpx;
  transition: all 0.3s ease;
}

.sort-arrow.up {
  border-left: 7rpx solid transparent;
  border-right: 7rpx solid transparent;
  border-bottom: 14rpx solid #D9D9D9;
}

.sort-arrow.down {
  border-left: 6rpx solid transparent;
  border-right: 6rpx solid transparent;
  border-top: 14rpx solid #D9D9D9;
}

.sort-arrow.up.active {
  border-bottom-color: #FB490E;
}

.sort-arrow.down.active {
  border-top-color: #FB490E;
}

/* 商品列表 */
.product-list {
  flex: 1;
  padding: 80rpx 30rpx 20rpx 20rpx;
  overflow-y: auto;
  background-color: #FFFFFF;
}

.product-item {
  display: flex;
  align-items: flex-start;
  padding: 26rpx 0;
  border-bottom: 2rpx solid #ECECEC;
  cursor: pointer;
  margin-right: 20rpx;
}

.product-item:active {
  background-color: #f9f9f9;
}

.product-image-container {
  width: 154rpx;
  height: 154rpx;
  border-radius: 10rpx;
  overflow: hidden;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.product-image {
  width: 154rpx;
  height: 154rpx;
  border-radius: 10rpx;
  object-fit: cover;
  display: block;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 154rpx;
  min-width: 0;
  width: calc(100% - 184rpx);
}

.product-title {
  font-family: Inter;
  font-weight: 500;
  font-size: 24rpx;
  line-height: 32rpx;
  color: #000000;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  max-height: 64rpx;
}

.product-tags {
  margin-bottom: 16rpx;
}

.tag {
  background-color: rgba(217, 217, 217, 0.07);
  border: 2rpx solid #FF3F00;
  border-radius: 6rpx;
  color: #FF3F00;
  font-family: Inter;
  font-weight: 500;
  font-size: 20rpx;
  line-height: 24rpx;
  padding: 4rpx 8rpx;
  display: inline-block;
}

.product-price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.price {
  font-family: Inter;
  font-weight: 700;
  font-size: 32rpx;
  line-height: 38rpx;
  color: #0B0B0B;
  flex-shrink: 0;
}

.commission-wrapper {
  display: flex;
  align-items: center;
  background-color: #FFE4DB;
  border-radius: 4rpx;
  padding: 8rpx 16rpx;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.commission-label {
  font-family: Inter;
  font-weight: 600;
  font-size: 22rpx;
  line-height: 26rpx;
  color: #FF3F00;
  margin-right: 8rpx;
}

.commission-price {
  font-family: Inter;
  font-weight: 600;
  font-size: 26rpx;
  line-height: 32rpx;
  color: #FF3F00;
}
