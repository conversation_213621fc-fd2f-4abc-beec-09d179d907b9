/* iPhone 16 尺寸适配 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 搜索栏样式 */
.search-container {
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
  padding: 20rpx 32rpx 32rpx;
  border-radius: 0 0 24rpx 24rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-placeholder {
  color: #999;
}

.search-btn {
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 500;
}

/* 功能图标区域 */
.function-icons {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 32rpx 24rpx;
  margin: 24rpx 24rpx 0;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.icon {
  width: 48rpx;
  height: 48rpx;
}

.icon-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.expand-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #666;
  font-size: 24rpx;
}

.expand-icon {
  font-size: 32rpx;
  margin-top: 8rpx;
  transform: rotate(90deg);
}

/* 分类标签 */
.category-tabs {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 24rpx;
  margin: 16rpx 24rpx 0;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.tab-item {
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 32rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #ff6b35;
  font-weight: 600;
}

.tab-item.highlight {
  color: #ff6b35;
  font-weight: 600;
}

/* 商品列表 */
.product-list {
  padding: 24rpx;
  flex: 1;
}

.product-item {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image-container {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.product-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-tags {
  margin-bottom: 16rpx;
}

.tag {
  background-color: #ff6b35;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
}

.product-price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: 600;
}

.commission {
  font-size: 24rpx;
  color: #ff6b35;
  background-color: #fff2f0;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ffccc7;
}
