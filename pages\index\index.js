// index.js
Page({
  data: {
    // 状态管理
    selectedIconIndex: 0, // 选中的图标索引
    selectedCategoryIndex: 0, // 选中的分类索引
    selectedSortIndex: 1, // 选中的排序索引（默认销量）
    isExpanded: false, // 是否展开更多图标

    // 商品数据
    products: [
      {
        id: 1,
        title: "同程风物严选好物推荐的精选好推荐物推荐的精选好物推",
        image: "/images/product-1.png",
        price: "39.9",
        commission: "10.9",
        tags: ["专属补贴"]
      },
      {
        id: 2,
        title: "同程风物严选好物推荐的精选好推荐物推荐的精选好物推",
        image: "/images/product-1.png",
        price: "39.9",
        commission: "10.9",
        tags: ["专属补贴"]
      },
      {
        id: 3,
        title: "同程风物严选好物推荐的精选好推荐物推荐的精选好物推",
        image: "/images/product-1.png",
        price: "39.9",
        commission: "10.9",
        tags: ["专属补贴"]
      },
      {
        id: 4,
        title: "同程风物严选好物推荐的精选好推荐物推荐的精选好物推",
        image: "/images/product-1.png",
        price: "39.9",
        commission: "10.9",
        tags: ["专属补贴"]
      },
      {
        id: 5,
        title: "同程风物严选好物推荐的精选好推荐物推荐的精选好物推",
        image: "/images/product-1.png",
        price: "39.9",
        commission: "10.9",
        tags: ["专属补贴"]
      },
      {
        id: 6,
        title: "同程风物严选好物推荐的精选好推荐物推荐的精选好物推",
        image: "/images/product-1.png",
        price: "39.9",
        commission: "10.9",
        tags: ["专属补贴"]
      }
    ]
  },

  onLoad() {
    // 页面加载时的逻辑
    console.log('页面加载完成');

    // 获取系统信息，设置状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;
    this.setData({
      statusBarHeight: statusBarHeight
    });
  },

  onShow() {
    // 页面显示时的逻辑
  },

  onReady() {
    // 页面初次渲染完成
  },

  // 搜索功能
  onSearchInput(e) {
    const value = e.detail.value;
    console.log('搜索内容:', value);
  },

  // 搜索按钮点击
  onSearchTap() {
    console.log('点击搜索按钮');
  },

  // 功能图标点击
  onIconTap(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    console.log('点击功能图标:', index);
    this.setData({
      selectedIconIndex: index
    });
  },

  // 左侧分类点击
  onCategoryTap(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const category = e.currentTarget.dataset.category;
    console.log('点击分类:', category, index);
    this.setData({
      selectedCategoryIndex: index
    });
  },

  // 排序标签点击
  onSortTap(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const sort = e.currentTarget.dataset.sort;
    console.log('点击排序:', sort, index);
    this.setData({
      selectedSortIndex: index
    });
  },

  // 商品点击
  onProductTap(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('点击商品:', productId);
    // 跳转到商品详情页
    // wx.navigateTo({
    //   url: `/pages/product/product?id=${productId}`
    // });
  },

  // 展开按钮点击
  onExpandTap() {
    console.log('点击展开按钮');
    this.setData({
      isExpanded: !this.data.isExpanded
    });
  }
})
