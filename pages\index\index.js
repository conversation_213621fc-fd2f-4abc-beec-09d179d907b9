// index.js
Page({
  data: {
    products: [
      {
        id: 1,
        title: "精华水乳口红套装粉底遮瑕精华水乳口红套装粉底遮瑕",
        image: "/images/product1.png",
        price: "39.9",
        commission: "10.9",
        tags: ["专属红包"]
      },
      {
        id: 2,
        title: "精华水乳口红套装粉底遮瑕精华水乳口红套装粉底遮瑕",
        image: "/images/product2.png", 
        price: "39.9",
        commission: "10.9",
        tags: ["专属红包"]
      },
      {
        id: 3,
        title: "精华水乳口红套装粉底遮瑕精华水乳口红套装粉底遮瑕",
        image: "/images/product3.png",
        price: "39.9", 
        commission: "10.9",
        tags: ["专属红包"]
      },
      {
        id: 4,
        title: "精华水乳口红套装粉底遮瑕精华水乳口红套装粉底遮瑕",
        image: "/images/product4.png",
        price: "39.9",
        commission: "10.9", 
        tags: ["专属红包"]
      },
      {
        id: 5,
        title: "精华水乳口红套装粉底遮瑕精华水乳口红套装粉底遮瑕",
        image: "/images/product5.png",
        price: "39.9",
        commission: "10.9",
        tags: ["专属红包"]
      }
    ]
  },

  onLoad() {
    // 页面加载时的逻辑
    console.log('页面加载完成');
  },

  onShow() {
    // 页面显示时的逻辑
  },

  onReady() {
    // 页面初次渲染完成
  },

  // 搜索功能
  onSearchInput(e) {
    const value = e.detail.value;
    console.log('搜索内容:', value);
  },

  // 搜索按钮点击
  onSearchTap() {
    console.log('点击搜索按钮');
  },

  // 功能图标点击
  onIconTap(e) {
    const index = e.currentTarget.dataset.index;
    console.log('点击功能图标:', index);
  },

  // 分类标签点击
  onTabTap(e) {
    const index = e.currentTarget.dataset.index;
    console.log('点击分类标签:', index);
  },

  // 商品点击
  onProductTap(e) {
    const productId = e.currentTarget.dataset.id;
    console.log('点击商品:', productId);
    // 跳转到商品详情页
    // wx.navigateTo({
    //   url: `/pages/product/product?id=${productId}`
    // });
  },

  // 展开按钮点击
  onExpandTap() {
    console.log('点击展开按钮');
  }
})
