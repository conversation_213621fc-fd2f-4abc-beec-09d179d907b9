<!--index.wxml-->
<view class="container">
  <!-- 顶部渐变背景 -->
  <view class="header-background">
    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="search-box">
        <image class="search-icon" src="/images/search-icon.svg"></image>
        <input class="search-input" placeholder="请输入商品名或者品牌名" placeholder-class="search-placeholder" bindinput="onSearchInput" />
        <view class="search-btn" bindtap="onSearchTap">搜索</view>
      </view>
    </view>

    <!-- 功能图标区域 -->
    <view class="function-icons">
      <view class="icon-item" bindtap="onIconTap" data-index="0">
        <view class="icon-wrapper">
          <image class="icon" src="/images/avatar-24.png"></image>
        </view>
        <text class="icon-text">美妆护肤</text>
      </view>
      <view class="icon-item" bindtap="onIconTap" data-index="1">
        <view class="icon-wrapper">
          <image class="icon" src="/images/avatar-8.png"></image>
        </view>
        <text class="icon-text">服饰内衣</text>
      </view>
      <view class="icon-item" bindtap="onIconTap" data-index="2">
        <view class="icon-wrapper">
          <image class="icon" src="/images/avatar-30.png"></image>
        </view>
        <text class="icon-text">食品生鲜</text>
      </view>
      <view class="icon-item" bindtap="onIconTap" data-index="3">
        <view class="icon-wrapper">
          <image class="icon" src="/images/avatar-24.png"></image>
        </view>
        <text class="icon-text">母婴玩具</text>
      </view>
      <view class="icon-item" bindtap="onIconTap" data-index="4">
        <view class="icon-wrapper">
          <image class="icon" src="/images/avatar-8.png"></image>
        </view>
        <text class="icon-text">数码家电</text>
      </view>
      <view class="expand-btn" bindtap="onExpandTap">
        <text class="expand-text">展开</text>
        <image class="expand-icon" src="/images/align-right-icon.svg"></image>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 左侧分类栏 -->
    <view class="left-sidebar">
      <view class="category-item active" bindtap="onCategoryTap" data-category="全部美妆">全部美妆</view>
      <view class="category-item" bindtap="onCategoryTap" data-category="精华水乳">精华水乳</view>
      <view class="category-item" bindtap="onCategoryTap" data-category="口红唇釉">口红唇釉</view>
      <view class="category-item" bindtap="onCategoryTap" data-category="粉底遮瑕">粉底遮瑕</view>
      <view class="category-item" bindtap="onCategoryTap" data-category="精华水乳">精华水乳</view>
      <view class="category-item" bindtap="onCategoryTap" data-category="口红唇釉">口红唇釉</view>
      <view class="category-item" bindtap="onCategoryTap" data-category="粉底遮瑕">粉底遮瑕</view>
      <view class="category-item" bindtap="onCategoryTap" data-category="精华水乳">精华水乳</view>
      <view class="category-item" bindtap="onCategoryTap" data-category="口红唇釉">口红唇釉</view>
      <view class="category-item" bindtap="onCategoryTap" data-category="粉底遮瑕">粉底遮瑕</view>
    </view>

    <!-- 右侧内容区域 -->
    <view class="right-content">
      <!-- 排序标签 -->
      <view class="sort-tabs">
        <view class="sort-item" bindtap="onSortTap" data-sort="综合">综合</view>
        <view class="sort-item active" bindtap="onSortTap" data-sort="销量">
          <text>销量</text>
          <view class="sort-arrow up"></view>
        </view>
        <view class="sort-item" bindtap="onSortTap" data-sort="佣金">
          <text>佣金</text>
          <view class="sort-arrow down"></view>
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="product-list">
        <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
          <view class="product-image-container">
            <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
          </view>
          <view class="product-info">
            <view class="product-title">{{item.title}}</view>
            <view class="product-tags">
              <text class="tag">专属补贴</text>
            </view>
            <view class="product-price-row">
              <text class="price">¥{{item.price}}</text>
              <view class="commission-wrapper">
                <text class="commission-label">定向高佣</text>
                <text class="commission-price">¥{{item.commission}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
