<!--index.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/images/search.png"></image>
      <input class="search-input" placeholder="请输入商品名或者商品牌名" placeholder-class="search-placeholder" bindinput="onSearchInput" />
      <view class="search-btn" bindtap="onSearchTap">搜索</view>
    </view>
  </view>

  <!-- 功能图标区域 -->
  <view class="function-icons">
    <view class="icon-item" bindtap="onIconTap" data-index="0">
      <view class="icon-wrapper">
        <image class="icon" src="/images/icon1.png"></image>
      </view>
      <text class="icon-text">全球中转</text>
    </view>
    <view class="icon-item" bindtap="onIconTap" data-index="1">
      <view class="icon-wrapper">
        <image class="icon" src="/images/icon2.png"></image>
      </view>
      <text class="icon-text">服饰内衣</text>
    </view>
    <view class="icon-item" bindtap="onIconTap" data-index="2">
      <view class="icon-wrapper">
        <image class="icon" src="/images/icon3.png"></image>
      </view>
      <text class="icon-text">食品生鲜</text>
    </view>
    <view class="icon-item" bindtap="onIconTap" data-index="3">
      <view class="icon-wrapper">
        <image class="icon" src="/images/icon4.png"></image>
      </view>
      <text class="icon-text">母婴玩具</text>
    </view>
    <view class="icon-item" bindtap="onIconTap" data-index="4">
      <view class="icon-wrapper">
        <image class="icon" src="/images/icon5.png"></image>
      </view>
      <text class="icon-text">数码家电</text>
    </view>
    <view class="expand-btn" bindtap="onExpandTap">
      <text>展开</text>
      <text class="expand-icon">≡</text>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <view class="tab-item active" bindtap="onTabTap" data-index="0">全部美妆</view>
    <view class="tab-item" bindtap="onTabTap" data-index="1">综合</view>
    <view class="tab-item highlight" bindtap="onTabTap" data-index="2">销量 ▲</view>
    <view class="tab-item" bindtap="onTabTap" data-index="3">佣金 ↓</view>
  </view>

  <!-- 商品列表 -->
  <view class="product-list">
    <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
      <view class="product-image-container">
        <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
      </view>
      <view class="product-info">
        <view class="product-title">{{item.title}}</view>
        <view class="product-tags">
          <text class="tag">专属红包</text>
        </view>
        <view class="product-price-row">
          <text class="price">¥{{item.price}}</text>
          <text class="commission">定向高佣 ¥{{item.commission}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
